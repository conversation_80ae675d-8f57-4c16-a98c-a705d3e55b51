import { useState, useEffect, useCallback } from 'react';

import type { BaseNodeData } from '../types/baseNode.types';

export interface UseFlowWindowResult {
  connecting: { from: string; fromPos?: { x: number; y: number } } | null;


  setConnecting: React.Dispatch<React.SetStateAction<{ from: string; fromPos?: { x: number; y: number } } | null>>;


  mousePos: { x: number; y: number } | null;


  setMousePos: React.Dispatch<React.SetStateAction<{ x: number; y: number } | null>>;


  handleOutputPortClick: (node: BaseNodeData) => void;


  handleInputPortClick: (
                            node: BaseNodeData, 
                            setNodes: React.Dispatch<React.SetStateAction<BaseNodeData[]>>
                        ) => void;


  onMove: (e: MouseEvent) => void;


  onKey: (e: KeyboardEvent) => void;


  handleBackgroundClick: () => void;
}

export function useFlowWindow() : UseFlowWindowResult {
  const [connecting, setConnecting] = useState<{ from: string; fromPos?: { x: number; y: number } } | null>(null);
  const [mousePos, setMousePos] = useState<{ x: number; y: number } | null>(null);

  const onMove = useCallback((e: MouseEvent) => setMousePos({ x: e.clientX, y: e.clientY }), []);
  const onKey = useCallback((e: KeyboardEvent) => { if (e.key === 'Escape') setConnecting(null); }, []);
  const handleBackgroundClick = useCallback(() => { if (connecting) setConnecting(null); }, [connecting]);

  const handleOutputPortClick = useCallback((node: BaseNodeData) => {
    const outX = node.position.x + 180 - 4;
    const outY = node.position.y + 56 / 2;
    setConnecting({ from: node.NodeId, fromPos: { x: outX, y: outY } });
    setMousePos(null);
  }, []);

  const handleInputPortClick = useCallback(
    (
        node: BaseNodeData, 
        setNodes: React.Dispatch<React.SetStateAction<BaseNodeData[]>>
    ) => {
    // Avbryt om vi inte är i "dra-läge" eller om du klickar på samma nod
    if (!connecting || connecting.from === node.NodeId) return;

    // ✅ Funktions-syntaxen garanterar att vi alltid får *senaste* nodes-state
    setNodes(prevNodes =>
      prevNodes.map(n => {
        if (n.NodeId === connecting.from) {
          return { ...n, OutputDestination: node.NodeId };
        }
        if (n.NodeId === node.NodeId) {
          return { ...n, SourceNode: connecting.from };
        }
        return n;
      })
    );

    // Släpp sladden
    setConnecting(null);
  },
  [connecting] // inga nodes här – vi använder ju funktionell uppdatering
);

  useEffect(() => {
    if (!connecting) return;
    window.addEventListener('mousemove', onMove);
    return () => window.removeEventListener('mousemove', onMove);
  }, [connecting, onMove]);

  return {
    connecting,
    setConnecting,
    mousePos,
    setMousePos,
    handleOutputPortClick,
    handleInputPortClick,
    onMove,
    onKey,
    handleBackgroundClick,
  };
}
