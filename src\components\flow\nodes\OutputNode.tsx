import React from 'react';
import { BaseNode } from './BaseNode/BaseNode';
import type { BaseNodeData } from '../../../types/baseNode.types';
import { GetNodeId } from '../../../utils/GetNodeId';
interface OutputNodeProps extends Omit<BaseNodeData, 'NodeType' | 'label' | 'accentColor'> {
  position: { x: number; y: number };
  onMouseDown: (e: React.MouseEvent, id: string) => void;
}

export const OutputNode: React.FC<OutputNodeProps> = ({
  position,
  onMouseDown,
  ...props
}) => {
  // Skapa ett unikt NodeId genom att kombinera label och GetNodeId()
  const NodeId = `Output${GetNodeId()}`;

  return (
    <BaseNode
      NodeId={NodeId}
      position={position}
      onMouseDown={onMouseDown}
      NodeType="output"
      label="Output"
      accentColor="#ff6978"
      {...props}
    />
  );
};

// NodeId kommer nu endast från BaseNodeData och dupliceras inte längre i OutputNodeProps.
