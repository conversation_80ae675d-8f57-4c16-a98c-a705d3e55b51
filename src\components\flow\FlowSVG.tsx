import React from 'react';
import { styled } from '@mui/material/styles';

export type FlowSVGProps = {
  nodes: BaseNodeData[];
  onBreakConnection?: (fromNodeId: string, toNodeId: string) => void;
};

const SVGContainer = styled('svg')({
  position: 'absolute',
  width: '100%',
  height: '100%',
  pointerEvents: 'none',
});

const NODE_WIDTH = 180;
const NODE_HEIGHT = 56;
const PORT_OFFSET_Y = NODE_HEIGHT / 2;
const OUTPUT_PORT_OFFSET_X = NODE_WIDTH - 4; // right port center
const INPUT_PORT_OFFSET_X = 4; // left port center

import type { BaseNodeData } from '../../types/baseNode.types';


const FlowSVG: React.FC<FlowSVGProps> = ({ nodes, onBreakConnection }) => (
  <SVGContainer>
    {nodes.map(source => {
      if (!source.OutputDestination) return null;
      const target = nodes.find(n => n.NodeId === source.OutputDestination);
      if (!target) return null;
      const x1 = source.position.x + OUTPUT_PORT_OFFSET_X;
      const y1 = source.position.y + PORT_OFFSET_Y;
      const x2 = target.position.x + INPUT_PORT_OFFSET_X;
      const y2 = target.position.y + PORT_OFFSET_Y;
      const mx = (x1 + x2) / 2;
      const my = (y1 + y2) / 2;
      return (
        <g key={source.NodeId + '-' + target.NodeId}>
          <line
            x1={x1}
            y1={y1}
            x2={x2}
            y2={y2}
            stroke="#5b9cff"
            strokeWidth={3}
            markerEnd="url(#arrowhead)"
          />
          {/* Break connection button */}
          {onBreakConnection && (
            <g
              style={{ cursor: 'pointer' }}
              onClick={() => onBreakConnection(source.NodeId, target.NodeId)}
              onMouseDown={e => e.stopPropagation()}
            >
              <circle cx={mx} cy={my} r={12} fill="#fff" stroke="#e53e4c" strokeWidth={2} style={{ pointerEvents: 'all' }} />
              <text
                x={mx}
                y={my + 4}
                textAnchor="middle"
                fontSize={16}
                fontWeight="bold"
                fill="#e53e4c"
                pointerEvents="none"
              >
                ×
              </text>
            </g>
          )}
        </g>
      );
    })}
    {/* Arrowhead marker */}
    <defs>
      <marker id="arrowhead" markerWidth="8" markerHeight="8" refX="8" refY="4" orient="auto" markerUnits="strokeWidth">
        <path d="M0,0 L8,4 L0,8" fill="#5b9cff" />
      </marker>
    </defs>
  </SVGContainer>
);

export default FlowSVG;
