import React from 'react';
import { BaseNode } from './BaseNode/BaseNode';
import type { BaseNodeData } from '../../../types/baseNode.types';

import { GetNodeId } from '../../../utils/GetNodeId';

interface ToolNodeProps extends Omit<BaseNodeData, 'NodeType' | 'label' | 'accentColor' | 'NodeId'> {
  position: { x: number; y: number };
  onMouseDown: (e: React.MouseEvent, id: string) => void;
}

export const ToolNode: React.FC<ToolNodeProps> = ({
  position,
  onMouseDown,
  ...props
}) => {
  const NodeId = `Tool${GetNodeId()}`;

  return (
    <BaseNode
      NodeId={NodeId}
      position={position}
      onMouseDown={onMouseDown}
      NodeType="tool"
      label="Tool"
      accentColor="#f8b84e"
      {...props}
    />
  );
};
