import React from 'react';
import { BaseNode } from './BaseNode/BaseNode';
import type { BaseNodeData } from '../../../types/baseNode.types';

import { GetNodeId } from '../../../utils/GetNodeId';

interface InputNodeProps extends Omit<BaseNodeData, 'NodeType' | 'label' | 'accentColor' | 'NodeId'> {
  position: { x: number; y: number };
  onMouseDown: (e: React.MouseEvent, id: string) => void;
}

export const InputNode: React.FC<InputNodeProps> = ({
  position,
  onMouseDown,
  ...props
}) => {
  const NodeId = `Input${GetNodeId()}`;

  return (
    <BaseNode
      NodeId={NodeId}
      position={position}
      onMouseDown={onMouseDown}
      NodeType="input"
      label="Input"
      accentColor="#4e8cff"
      {...props}
    />
  );
};
