import { useState } from 'react';
import type { FC } from 'react';
import { styled } from '@mui/material/styles';
import Sidebar from '../sidebar/Sidebar';
import ChatWindow from './ChatWindow';
import FlowWindow from '../flow/FlowWindow';

const ChatLayout: FC = () => {
  const [activeTab, setActiveTab] = useState('CHATS');
  const ChatLayoutContainer = styled('div')({
    display: 'flex',
    height: '100vh',
    backgroundColor: '#121212',
    color: 'white',
  });

  return (
    <ChatLayoutContainer>
      <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />
      {activeTab === 'CHATS' ? <ChatWindow /> : <FlowWindow />}
    </ChatLayoutContainer>
  );
};

export default ChatLayout;