import React from 'react';
import { BaseNode } from './BaseNode/BaseNode';
import type { BaseNodeData } from '../../../types/baseNode.types';

import { GetNodeId } from '../../../utils/GetNodeId';

interface AgentNodeProps extends Omit<BaseNodeData, 'NodeType' | 'label' | 'accentColor' | 'NodeId'> {
  position: { x: number; y: number };
  onMouseDown: (e: React.MouseEvent, id: string) => void;
}

export const AgentNode: React.FC<AgentNodeProps> = ({
  position,
  onMouseDown,
  ...props
}) => {
  const NodeId = `Agent${GetNodeId()}`;

  return (
    <BaseNode
      NodeId={NodeId}
      position={position}
      onMouseDown={onMouseDown}
      NodeType="agent"
      label="Agent"
      accentColor="#47c78a"
      {...props}
    />
  );
};
