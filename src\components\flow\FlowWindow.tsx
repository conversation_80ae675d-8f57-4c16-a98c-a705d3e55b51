import React, { useRef } from 'react';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import BuildIcon from '@mui/icons-material/Build';
import { styled } from '@mui/material/styles';
import { InputNode } from './nodes/InputNode';
import { AgentNode } from './nodes/AgentNode';
import { ToolNode } from './nodes/ToolNode';
import { OutputNode } from './nodes/OutputNode';
import { useFlowNodes } from '../../hooks/useFlowNodes';
import FlowSVG from './FlowSVG';
import FlowMenu from './FlowMenu/FlowMenu';
import { useFlowMenu } from '../../hooks/useFlowMenu';
import { FlowMenuToolbar } from './toolbar/Toolbar';
import { type BaseNodeData } from '../../types/baseNode.types';
import { useFlowWindow } from '../../hooks/useFlowWindow';



const FlowWindow: React.FC = () => {
  const { expanded: menuExpanded, toggle: toggleMenu } = useFlowMenu(true);
  const { nodes, setNodes, onMouseDown } = useFlowNodes();
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Use the custom hook for all logic/state
  const {
    connecting,
    setConnecting,
    mousePos,
    setMousePos,
    handleOutputPortClick,
    handleInputPortClick,
    onMove,
    onKey,
    handleBackgroundClick,
  } = useFlowWindow();

  // Only UI logic for adding a node remains here
  const handleAddNode = (type: string) => {
    let nodeType: 'input' | 'agent' | 'tool' | 'output' = 'input';
    let label = '';
    switch (type) {
      case 'Input':
        nodeType = 'input';
        label = 'Input';
        break;
      case 'Agent':
        nodeType = 'agent';
        label = 'Agent';
        break;
      case 'Tool':
        nodeType = 'tool';
        label = 'Tool';
        break;
      case 'Output':
        nodeType = 'output';
        label = 'Output';
        break;
      default:
        nodeType = 'input';
        label = type;
    }
    const NodeId = Math.random().toString(36).substr(2, 9);
    let x = 200 + Math.random() * 200;
    let y = 120 + Math.random() * 200;
    setNodes(nodes => [
      ...nodes,
      { NodeId, position: { x, y }, NodeType: nodeType, label }
    ]);
  };



  const FlowWindowContainer = styled('div')({
    position: 'relative',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(135deg, #23272f 0%, #2c3340 100%)',
    overflow: 'hidden',
    minHeight: 600,
  });

  const GridOverlay = styled('div')({
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    pointerEvents: 'none',
    backgroundImage:
      'linear-gradient(rgba(255,255,255,0.04) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,0.04) 1px, transparent 1px)',
    backgroundSize: '32px 32px',
    zIndex: 1,
  });

  const NodeArea = styled('div')({
    position: 'relative',
    width: '100%',
    height: '100%',
    zIndex: 2,
    boxShadow: '0 8px 32px 0 rgba(0,0,0,0.10)',
    borderRadius: 16,
  });

  return (
    <FlowWindowContainer ref={containerRef}>
      <FlowMenu expanded={menuExpanded} onToggle={toggleMenu} onAddNode={handleAddNode} />
      <GridOverlay />
      <NodeArea onClick={handleBackgroundClick}>
        <FlowSVG
          nodes={nodes}
          onBreakConnection={(fromNodeId, toNodeId) => {
            setNodes(nodes => nodes.map(n => {
              if (n.NodeId === fromNodeId) return { ...n, OutputDestination: undefined };
              if (n.NodeId === toNodeId) return { ...n, SourceNode: undefined };
              return n;
            }));
          }}
        />
        {/* Ghost line when connecting */}
        {connecting && connecting.fromPos && mousePos && (
          (() => {
            let svgX = mousePos.x;
            let svgY = mousePos.y;
            if (containerRef.current) {
              const rect = containerRef.current.getBoundingClientRect();
              svgX = mousePos.x - rect.left;
              svgY = mousePos.y - rect.top;
            }
            return (
              <svg style={{ position: 'absolute', left: 0, top: 0, width: '100%', height: '100%', pointerEvents: 'none', zIndex: 100 }}>
                <line
                  x1={connecting.fromPos.x}
                  y1={connecting.fromPos.y}
                  x2={svgX}
                  y2={svgY}
                  stroke="#3578e5"
                  strokeWidth={2}
                  strokeDasharray="6 4"
                />
              </svg>
            );
          })()
        )}
        {nodes.map(node => {
          const nodeProps = {
            NodeId: node.NodeId,
            position: node.position,
            label: node.NodeType,
            onMouseDown: (e: React.MouseEvent) => {
              // Prevent dragging when clicking on port handles
              if ((e.target as HTMLElement).closest('.port-handle')) return;
              onMouseDown(e, node.NodeId);
            },
            children: <>
              {/* Input port (left, docked) */}
              <div
                style={{
                  position: 'absolute',
                  left: -12,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: 32,
                  height: 32,
                  borderRadius: '50%',
                  background: connecting && connecting.from && node.NodeId !== connecting.from
                    ? 'radial-gradient(circle at 60% 40%, #e0f7fa 60%, #2ec4ff 100%)'
                    : 'radial-gradient(circle at 60% 40%, #fff 60%, #6db6ff 100%)',
                  border: (connecting && connecting.from && node.NodeId !== connecting.from) ? '3px solid #2ec4ff' : '3px solid #6db6ff',
                  boxShadow: '0 0 22px 6px #2ec4ff77, 0 2px 8px #0001',
                  cursor: 'pointer',
                  zIndex: 30,
                  pointerEvents: 'auto',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: 'background 0.2s, border 0.2s',
                  fontFamily: 'Inter, Segoe UI, Arial, sans-serif',
                  WebkitFontSmoothing: 'antialiased',
                }}
                title="Input port"
                onMouseDown={e => e.stopPropagation()}
                onClick={e => {
                  e.stopPropagation();
                  handleInputPortClick(node, setNodes);
                }}
              >
                {/* Icon for input port */}
                {(() => {
                  switch (node.NodeType) {
                    case 'input':
                      return <ArrowDownwardIcon sx={{ fontSize: 20, color: '#2196f3' }} />;
                    case 'agent':
                      return <SmartToyIcon sx={{ fontSize: 20, color: '#2196f3' }} />;
                    case 'tool':
                      return <BuildIcon sx={{ fontSize: 20, color: '#2196f3' }} />;
                    case 'output':
                      return <ArrowUpwardIcon sx={{ fontSize: 20, color: '#2196f3' }} />;
                    default:
                      return <span style={{fontSize: 18, color: '#2196f3'}}>•</span>;
                  }
                })()}
              </div>
              {/* Output port (right, docked) */}
              <div
                style={{
                  position: 'absolute',
                  right: -12,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: 32,
                  height: 32,
                  borderRadius: '50%',
                  background: connecting && connecting.from === node.NodeId
                    ? 'radial-gradient(circle at 60% 40%, #e0f7fa 60%, #2ec4ff 100%)'
                    : 'radial-gradient(circle at 60% 40%, #fff 60%, #6db6ff 100%)',
                  border: (connecting && connecting.from === node.NodeId) ? '3px solid #2ec4ff' : '3px solid #6db6ff',
                  boxShadow: '0 0 22px 6px #2ec4ff77, 0 2px 8px #0001',
                  cursor: 'pointer',
                  zIndex: 30,
                  pointerEvents: 'auto',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: 'background 0.2s, border 0.2s',
                  fontFamily: 'Inter, Segoe UI, Arial, sans-serif',
                  WebkitFontSmoothing: 'antialiased',
                }}
                title="Output port"
                onMouseDown={e => e.stopPropagation()}
                onClick={e => {
                  e.stopPropagation();
                  handleOutputPortClick(node);
                }}
              >
                {/* Icon for output port */}
                {(() => {
                  switch (node.NodeType) {
                    case 'input':
                      return <ArrowDownwardIcon sx={{ fontSize: 20, color: '#1565c0' }} />;
                    case 'agent':
                      return <SmartToyIcon sx={{ fontSize: 20, color: '#1565c0' }} />;
                    case 'tool':
                      return <BuildIcon sx={{ fontSize: 20, color: '#1565c0' }} />;
                    case 'output':
                      return <ArrowUpwardIcon sx={{ fontSize: 20, color: '#1565c0' }} />;
                    default:
                      return <span style={{fontSize: 18, color: '#1565c0'}}>•</span>;
                  }
                })()}
              </div>
            </>
          };
          switch (node.NodeType) {
            case 'input':
              return <InputNode key={node.NodeId} {...nodeProps} />;
            case 'agent':
              return <AgentNode key={node.NodeId} {...nodeProps} />;
            case 'tool':
              return <ToolNode key={node.NodeId} {...nodeProps} />;
            case 'output':
              return <OutputNode key={node.NodeId} {...nodeProps} />;
            default:
              return null;
          }
        })}
      </NodeArea>
      <div
        style={{
          position: 'absolute',
          left: 0,
          bottom: 0,
          width: menuExpanded ? 'calc(100% - 280px)' : '100%',
          right: menuExpanded ? 280 : 0,
          zIndex: 20,
          transition: 'width 0.3s cubic-bezier(.4,1.2,.6,1), right 0.3s cubic-bezier(.4,1.2,.6,1)',
        }}
      >
        <FlowMenuToolbar expanded={menuExpanded} />
      </div>
    </FlowWindowContainer>
  );
};

export default FlowWindow;
